# 微信号收集系统

一个基于 Vue.js 3 和 Node.js 的微信号收集和管理系统，支持用户提交微信号和管理员后台管理功能。

## 功能特性

### 用户功能

- 微信号输入和验证
- 提交成功后显示欢迎页面
- 响应式设计，支持手机端操作

### 管理员功能

- 管理员登录（用户名：admin，密码：ww112233）
- 查看所有收集到的微信号（带序号显示）
- 显示用户提交时的 IP 地址
- 一键复制微信号功能
- 删除微信号功能（支持确认对话框）
- 导出 Excel 表格功能（包含序号、微信号、提交时间、IP 地址、记录 ID）
- 显示提交时间统计

### 技术特性

- 前端：Vue.js 3 + Vite + Vue Router
- 后端：Node.js + Express
- 数据存储：JSON 文件（轻量级）
- Excel 导出：xlsx + file-saver
- 响应式设计，移动端优先
- RESTful API 设计

## 项目结构

```
wechat-login/
├── frontend/                 # Vue.js前端项目
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── router/          # 路由配置
│   │   └── assets/          # 静态资源
│   ├── package.json
│   └── vite.config.js
├── backend/                  # Node.js后端API
│   ├── data/                # 数据存储目录
│   ├── server.js            # 服务器入口
│   └── package.json
└── README.md
```

### 路由配置

- `/` - 用户登录页面（主页）
- `/login` - 用户登录页面（别名）
- `/welcome` - 提交成功页面
- `/admin` - 管理员登录页面
- `/admin/dashboard` - 管理员后台（需要登录）

## 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

1. 安装后端依赖：

```bash
cd backend
npm install
```

2. 安装前端依赖：

```bash
cd frontend
npm install
```

### 开发环境运行

1. 启动后端服务（端口 3001）：

```bash
cd backend
npm run dev
```

2. 启动前端开发服务（端口 3000）：

```bash
cd frontend
npm run dev
```

3. 访问应用：

- 用户界面：http://localhost:3000 或 http://localhost:3000/login
- 管理员界面：http://localhost:3000/admin

### 生产环境部署

#### 方法一：分离部署

1. 构建前端：

```bash
cd frontend
npm run build
```

2. 部署前端静态文件：
   将 `frontend/dist` 目录下的文件部署到 Web 服务器（如 Nginx）

3. 部署后端 API：

```bash
cd backend
npm install --production
npm start
```

#### 方法二：集成部署

1. 构建前端：

```bash
cd frontend
npm run build
```

2. 将构建文件复制到后端：

```bash
cp -r frontend/dist/* backend/public/
```

3. 修改后端服务器配置以提供静态文件服务

4. 启动后端服务：

```bash
cd backend
npm start
```

## API 接口文档

### 用户接口

#### 提交微信号

- **URL**: `POST /api/wechat`
- **参数**:
  ```json
  {
    "wechatId": "用户微信号"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "微信号提交成功",
    "data": {
      "id": "uuid",
      "wechatId": "微信号",
      "submitTime": "2024-01-01T10:00:00.000Z",
      "ipAddress": "*************"
    }
  }
  ```

### 管理员接口

#### 管理员登录

- **URL**: `POST /api/admin/login`
- **参数**:
  ```json
  {
    "username": "admin",
    "password": "ww112233"
  }
  ```

#### 获取微信号列表

- **URL**: `GET /api/wechat`
- **响应**:
  ```json
  {
    "success": true,
    "wechatAccounts": [
      {
        "id": "uuid",
        "wechatId": "微信号",
        "submitTime": "2024-01-01T10:00:00.000Z",
        "ipAddress": "*************"
      }
    ]
  }
  ```

#### 删除微信号

- **URL**: `DELETE /api/wechat/:id`
- **参数**: URL 参数中的微信号 ID
- **响应**:
  ```json
  {
    "success": true,
    "message": "微信号删除成功",
    "data": {
      "id": "uuid",
      "wechatId": "微信号",
      "submitTime": "2024-01-01T10:00:00.000Z",
      "ipAddress": "*************"
    }
  }
  ```

## 配置说明

### 前端配置

- 开发服务器端口：3000
- API 代理配置：自动代理 `/api` 请求到后端服务器

### 后端配置

- 服务器端口：3001（可通过环境变量 `PORT` 修改）
- 数据文件位置：`backend/data/wechat-accounts.json`
- 管理员账号：admin / ww112233

## 服务器部署建议

### 使用 PM2 进程管理

1. 安装 PM2：

```bash
npm install -g pm2
```

2. 创建 PM2 配置文件 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [
    {
      name: "wechat-login-api",
      script: "./backend/server.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        PORT: 3001,
      },
    },
  ],
};
```

3. 启动服务：

```bash
pm2 start ecosystem.config.js
```

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 数据备份

数据存储在 `backend/data/wechat-accounts.json` 文件中，建议定期备份此文件。

## 故障排除

### 常见问题

1. **端口占用**：确保 3000 和 3001 端口未被其他程序占用
2. **权限问题**：确保应用有读写数据文件的权限
3. **跨域问题**：确保前端代理配置正确

### 日志查看

- 开发环境：控制台直接显示日志
- 生产环境：使用 PM2 查看日志 `pm2 logs`

## 更新日志

### v1.1.0 (2024-06-14)

**新增功能：**

- ✅ 添加 IP 地址收集功能，自动记录用户提交时的真实 IP 地址
- ✅ 管理员后台显示 IP 地址信息
- ✅ Excel 导出包含 IP 地址列
- ✅ 修复 Vue Router History 模式 404 问题
- ✅ 优化 Nginx 配置，支持页面刷新

**技术改进：**

- 🔧 后端添加 IP 地址获取函数，支持多种代理环境
- 🔧 前端界面优化，IP 地址使用等宽字体显示
- 🔧 更新 API 文档，包含 IP 地址字段说明

### v1.0.0 (2024-06-13)

**初始版本：**

- 🎉 用户微信号提交功能
- 🎉 管理员后台管理功能
- 🎉 Excel 数据导出功能
- 🎉 响应式设计，移动端适配

## 许可证

MIT License
