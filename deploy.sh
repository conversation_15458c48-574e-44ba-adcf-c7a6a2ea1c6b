#!/bin/bash

# 微信登录系统宝塔部署脚本

echo "🚀 开始部署微信登录系统..."

# 1. 复制前端文件
echo "📦 复制前端文件..."
cp -r frontend/dist/* /www/wwwroot/ev.zhcv.cn/

# 2. 安装后端依赖
echo "📥 安装后端依赖..."
cd backend
npm install --production

# 3. 停止旧服务
echo "🛑 停止旧服务..."
pm2 stop wechat-api 2>/dev/null || true
pm2 delete wechat-api 2>/dev/null || true

# 4. 启动新服务
echo "🚀 启动后端服务..."
pm2 start server.js --name "wechat-api"

# 5. 保存PM2配置
echo "💾 保存PM2配置..."
pm2 save

# 6. 重载nginx
echo "🔄 重载nginx配置..."
nginx -t && systemctl reload nginx

# 7. 检查服务状态
echo "✅ 检查服务状态..."
pm2 status
curl -s http://localhost:3001/api/health

echo "🎉 部署完成！"
echo "📱 前端地址: http://ev.zhcv.cn"
echo "🔧 后端API: http://ev.zhcv.cn/api/health"
