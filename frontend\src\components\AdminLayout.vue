<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">管理后台</h2>
      </div>

      <nav class="sidebar-nav">
        <router-link
          to="/admin/dashboard"
          class="nav-item"
          :class="{ active: $route.path === '/admin/dashboard' }"
        >
          <span class="nav-icon">📊</span>
          <span class="nav-text">查询用户管理</span>
        </router-link>

        <router-link
          to="/admin/users"
          class="nav-item"
          :class="{ active: $route.path === '/admin/users' }"
        >
          <span class="nav-icon">👥</span>
          <span class="nav-text">管理员账号管理</span>
        </router-link>

        <router-link
          to="/admin/keys"
          class="nav-item"
          :class="{ active: $route.path === '/admin/keys' }"
        >
          <span class="nav-icon">🔑</span>
          <span class="nav-text">密钥管理</span>
        </router-link>

        <router-link
          to="/admin/medicine"
          class="nav-item"
          :class="{ active: $route.path === '/admin/medicine' }"
        >
          <span class="nav-icon">🌿</span>
          <span class="nav-text">中药药方管理</span>
        </router-link>
      </nav>

      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-name">{{ currentAdmin?.name || "管理员" }}</div>
          <div class="user-role">{{ getRoleText(currentAdmin?.role) }}</div>
          <button
            @click="showChangePasswordModal = true"
            class="change-password-btn"
          >
            <span class="change-password-icon">🔑</span>
            <span class="change-password-text">修改密码</span>
          </button>
        </div>
        <button @click="logout" class="logout-btn">
          <span class="logout-icon">🚪</span>
          <span class="logout-text">退出登录</span>
        </button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="content-header">
        <h1 class="page-title">{{ getPageTitle() }}</h1>
      </div>

      <div class="content-body">
        <router-view />
      </div>
    </div>

    <!-- 修改密码模态框 -->
    <div
      v-if="showChangePasswordModal"
      class="modal-overlay"
      @click="closeChangePasswordModal"
    >
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>修改管理员密码</h3>
          <button @click="closeChangePasswordModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="newPassword">新密码</label>
            <input
              id="newPassword"
              v-model="newPassword"
              type="password"
              placeholder="请输入新密码"
              class="form-input"
              @keyup.enter="changePassword"
            />
          </div>
          <div class="form-group">
            <label for="confirmPassword">确认密码</label>
            <input
              id="confirmPassword"
              v-model="confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              class="form-input"
              @keyup.enter="changePassword"
            />
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeChangePasswordModal" class="cancel-btn">
            取消
          </button>
          <button
            @click="changePassword"
            class="confirm-btn"
            :disabled="isChangingPassword"
          >
            {{ isChangingPassword ? "修改中..." : "确认修改" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "AdminLayout",
  data() {
    return {
      currentAdmin: null,
      showChangePasswordModal: false,
      newPassword: "",
      confirmPassword: "",
      isChangingPassword: false,
    };
  },
  mounted() {
    // 从localStorage获取当前管理员信息
    const adminInfo = localStorage.getItem("currentAdmin");
    if (adminInfo) {
      this.currentAdmin = JSON.parse(adminInfo);
    }
  },
  methods: {
    getRoleText(role) {
      const roleMap = {
        super_admin: "超级管理员",
        admin: "管理员",
        user: "普通用户",
      };
      return roleMap[role] || "未知角色";
    },

    getPageTitle() {
      const titleMap = {
        "/admin/dashboard": "查询用户管理",
        "/admin/users": "管理员账号管理",
        "/admin/keys": "密钥管理",
        "/admin/medicine": "中药药方管理",
      };
      return titleMap[this.$route.path] || "管理后台";
    },

    closeChangePasswordModal() {
      this.showChangePasswordModal = false;
      this.newPassword = "";
      this.confirmPassword = "";
    },

    async changePassword() {
      if (!this.newPassword || !this.confirmPassword) {
        alert("请填写完整的密码信息");
        return;
      }

      if (this.newPassword !== this.confirmPassword) {
        alert("两次输入的密码不一致");
        return;
      }

      if (this.newPassword.length < 6) {
        alert("密码长度不能少于6位");
        return;
      }

      this.isChangingPassword = true;

      try {
        const response = await axios.put(
          `/api/admin/users/${this.currentAdmin.id}/password`,
          {
            newPassword: this.newPassword,
          }
        );

        if (response.data.success) {
          alert("密码修改成功");
          this.closeChangePasswordModal();
        } else {
          alert(response.data.message || "密码修改失败");
        }
      } catch (error) {
        console.error("修改密码失败:", error);
        alert(error.response?.data?.message || "密码修改失败，请重试");
      } finally {
        this.isChangingPassword = false;
      }
    },

    logout() {
      localStorage.removeItem("adminLoggedIn");
      localStorage.removeItem("currentAdmin");
      this.$router.push("/admin");
    },
  },
};
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.admin-layout::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  opacity: 0.1;
  z-index: 0;
}

.sidebar {
  width: 300px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.sidebar-header {
  padding: 40px 25px 30px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
}

.logo {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  letter-spacing: -0.5px;
}

.sidebar-nav {
  flex: 1;
  padding: 25px 15px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin: 8px 0;
  color: #64748b;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.nav-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.nav-item:hover {
  color: #667eea;
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nav-item:hover::before {
  opacity: 0.08;
}

.nav-item.active {
  color: white;
  font-weight: 700;
  transform: translateX(8px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
}

.nav-item.active::before {
  opacity: 1;
}

.nav-icon {
  font-size: 22px;
  margin-right: 15px;
  width: 28px;
  text-align: center;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-size: 16px;
  letter-spacing: 0.3px;
}

.sidebar-footer {
  padding: 25px 20px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.03) 0%,
    rgba(118, 75, 162, 0.03) 100%
  );
}

.user-info {
  margin-bottom: 20px;
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.user-name {
  font-size: 17px;
  font-weight: 700;
  color: #334155;
  margin-bottom: 6px;
  letter-spacing: 0.3px;
}

.user-role {
  font-size: 13px;
  color: #667eea;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.change-password-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 12px;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3),
    0 2px 8px rgba(245, 158, 11, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.change-password-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.change-password-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  border-radius: 10px;
  pointer-events: none;
}

.change-password-btn:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 50%, #92400e 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4),
    0 4px 12px rgba(245, 158, 11, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(245, 158, 11, 0.2);
}

.change-password-btn:hover::before {
  left: 100%;
}

.change-password-btn:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.change-password-icon {
  margin-right: 8px;
  font-size: 14px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 16px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.logout-btn:hover::before {
  left: 100%;
}

.logout-btn:active {
  transform: translateY(0);
}

.logout-icon {
  margin-right: 10px;
  font-size: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.content-header {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  padding: 25px 35px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.content-header::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0.6;
}

.page-title {
  font-size: 32px;
  font-weight: 800;
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.5px;
  position: relative;
}

.content-body {
  flex: 1;
  padding: 35px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.02);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 10px 0;
  }

  .nav-item {
    flex-direction: column;
    min-width: 80px;
    text-align: center;
    padding: 10px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .nav-text {
    font-size: 12px;
  }

  .sidebar-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    margin-bottom: 0;
    text-align: left;
  }

  .logout-btn {
    width: auto;
    padding: 8px 16px;
  }

  .content-header {
    padding: 15px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .content-body {
    padding: 20px;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 10px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  width: 90%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.3px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.close-btn:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  transform: scale(1.1);
}

.modal-body {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  letter-spacing: 0.3px;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 15px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
    0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 30px 30px;
  justify-content: flex-end;
}

.cancel-btn,
.confirm-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cancel-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 50%, #374151 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(107, 114, 128, 0.3),
    0 2px 8px rgba(107, 114, 128, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cancel-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 50%, #1f2937 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4),
    0 4px 12px rgba(107, 114, 128, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #5b21b6 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3),
    0 2px 8px rgba(102, 126, 234, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 50%, #4c1d95 100%);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(102, 126, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}
</style>
