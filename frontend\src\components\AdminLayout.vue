<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h2 class="logo">管理后台</h2>
      </div>

      <nav class="sidebar-nav">
        <router-link
          to="/admin/dashboard"
          class="nav-item"
          :class="{ active: $route.path === '/admin/dashboard' }"
        >
          <span class="nav-icon">📊</span>
          <span class="nav-text">查询用户管理</span>
        </router-link>

        <router-link
          to="/admin/users"
          class="nav-item"
          :class="{ active: $route.path === '/admin/users' }"
        >
          <span class="nav-icon">👥</span>
          <span class="nav-text">管理员账号管理</span>
        </router-link>

        <router-link
          to="/admin/keys"
          class="nav-item"
          :class="{ active: $route.path === '/admin/keys' }"
        >
          <span class="nav-icon">🔑</span>
          <span class="nav-text">密钥管理</span>
        </router-link>

        <router-link
          to="/admin/medicine"
          class="nav-item"
          :class="{ active: $route.path === '/admin/medicine' }"
        >
          <span class="nav-icon">🌿</span>
          <span class="nav-text">中药药方管理</span>
        </router-link>
      </nav>

      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-name">{{ currentAdmin?.name || "管理员" }}</div>
          <div class="user-role">{{ getRoleText(currentAdmin?.role) }}</div>
        </div>
        <button @click="logout" class="logout-btn">
          <span class="logout-icon">🚪</span>
          <span class="logout-text">退出登录</span>
        </button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="content-header">
        <h1 class="page-title">{{ getPageTitle() }}</h1>
      </div>

      <div class="content-body">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdminLayout",
  data() {
    return {
      currentAdmin: null,
    };
  },
  mounted() {
    // 从localStorage获取当前管理员信息
    const adminInfo = localStorage.getItem("currentAdmin");
    if (adminInfo) {
      this.currentAdmin = JSON.parse(adminInfo);
    }
  },
  methods: {
    getRoleText(role) {
      const roleMap = {
        super_admin: "超级管理员",
        admin: "管理员",
        user: "普通用户",
      };
      return roleMap[role] || "未知角色";
    },

    getPageTitle() {
      const titleMap = {
        "/admin/dashboard": "查询用户管理",
        "/admin/users": "管理员账号管理",
        "/admin/keys": "密钥管理",
        "/admin/medicine": "中药药方管理",
      };
      return titleMap[this.$route.path] || "管理后台";
    },

    logout() {
      localStorage.removeItem("adminLoggedIn");
      localStorage.removeItem("currentAdmin");
      this.$router.push("/admin");
    },
  },
};
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.admin-layout::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  opacity: 0.1;
  z-index: 0;
}

.sidebar {
  width: 300px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.sidebar-header {
  padding: 40px 25px 30px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
}

.logo {
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  letter-spacing: -0.5px;
}

.sidebar-nav {
  flex: 1;
  padding: 25px 15px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin: 8px 0;
  color: #64748b;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.nav-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.nav-item:hover {
  color: #667eea;
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nav-item:hover::before {
  opacity: 0.08;
}

.nav-item.active {
  color: white;
  font-weight: 700;
  transform: translateX(8px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
}

.nav-item.active::before {
  opacity: 1;
}

.nav-icon {
  font-size: 22px;
  margin-right: 15px;
  width: 28px;
  text-align: center;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-size: 16px;
  letter-spacing: 0.3px;
}

.sidebar-footer {
  padding: 25px 20px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.03) 0%,
    rgba(118, 75, 162, 0.03) 100%
  );
}

.user-info {
  margin-bottom: 20px;
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.user-name {
  font-size: 17px;
  font-weight: 700;
  color: #334155;
  margin-bottom: 6px;
  letter-spacing: 0.3px;
}

.user-role {
  font-size: 13px;
  color: #667eea;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 16px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.logout-btn:hover::before {
  left: 100%;
}

.logout-btn:active {
  transform: translateY(0);
}

.logout-icon {
  margin-right: 10px;
  font-size: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.content-header {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  padding: 25px 35px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.content-header::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0.6;
}

.page-title {
  font-size: 32px;
  font-weight: 800;
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.5px;
  position: relative;
}

.content-body {
  flex: 1;
  padding: 35px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.02);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 10px 0;
  }

  .nav-item {
    flex-direction: column;
    min-width: 80px;
    text-align: center;
    padding: 10px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-item.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .nav-text {
    font-size: 12px;
  }

  .sidebar-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .user-info {
    margin-bottom: 0;
    text-align: left;
  }

  .logout-btn {
    width: auto;
    padding: 8px 16px;
  }

  .content-header {
    padding: 15px 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .content-body {
    padding: 20px;
  }
}
</style>
