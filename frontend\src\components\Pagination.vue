<template>
  <div class="pagination" v-if="totalPages > 1">
    <button
      class="pagination-btn prev-btn"
      :disabled="currentPage === 1"
      @click="goToPage(currentPage - 1)"
    >
      <span class="btn-icon">‹</span>
      <span class="btn-text">上一页</span>
    </button>

    <div class="pagination-numbers">
      <!-- 显示所有页码（简化版本） -->
      <button
        v-for="page in allPages"
        :key="page"
        class="pagination-number"
        :class="{ active: currentPage === page }"
        @click="goToPage(page)"
      >
        {{ page }}
      </button>
    </div>

    <button
      class="pagination-btn next-btn"
      :disabled="currentPage === totalPages"
      @click="goToPage(currentPage + 1)"
    >
      <span class="btn-text">下一页</span>
      <span class="btn-icon">›</span>
    </button>

    <div class="pagination-info">
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      <span class="total-info"> 总计 {{ totalItems }} 条记录 </span>
    </div>

    <div class="pagination-size">
      <label class="size-label">每页显示：</label>
      <select
        class="size-select"
        :value="pageSize"
        @change="changePageSize($event.target.value)"
      >
        <option value="10">10 条</option>
        <option value="20">20 条</option>
        <option value="50">50 条</option>
        <option value="100">100 条</option>
      </select>
    </div>
  </div>
</template>

<script>
export default {
  name: "Pagination",
  props: {
    currentPage: {
      type: Number,
      default: 1,
    },
    totalItems: {
      type: Number,
      required: true,
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    maxVisiblePages: {
      type: Number,
      default: 5,
    },
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalItems / this.pageSize);
    },

    allPages() {
      const pages = [];
      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }
      return pages;
    },
  },
  methods: {
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
        this.$emit("page-change", page);
      }
    },

    changePageSize(newSize) {
      const size = parseInt(newSize);
      this.$emit("size-change", size);
    },
  },
};
</script>

<style scoped>
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 20px 0;
  flex-wrap: wrap;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.pagination-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.btn-icon {
  font-size: 16px;
  font-weight: bold;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: #666;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.pagination-number:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

.pagination-number.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pagination-ellipsis {
  color: #999;
  font-weight: bold;
  padding: 0 8px;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
}

.page-info {
  font-weight: 600;
}

.total-info {
  color: #888;
}

.pagination-size {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.size-select {
  padding: 6px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.size-select:focus {
  outline: none;
  border-color: #667eea;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pagination {
    flex-direction: column;
    gap: 15px;
  }

  .pagination-numbers {
    order: -1;
  }

  .pagination-btn {
    padding: 12px 20px;
    font-size: 15px;
  }

  .pagination-info {
    order: 1;
  }

  .pagination-size {
    order: 2;
  }
}
</style>
