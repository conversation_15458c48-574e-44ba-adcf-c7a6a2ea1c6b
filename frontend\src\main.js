import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './assets/style.css'

// 1. Import the toolbar
import { initToolbar } from '@stagewise/toolbar';

// 2. Define your toolbar configuration
const stagewiseConfig = {
    plugins: [],
};

// 3. Initialize the toolbar when your app starts
// Framework-agnostic approach - call this when your app initializes
function setupStagewise() {
    // Only initialize once and only in development mode
    if (process.env.NODE_ENV === 'development' || import.meta.env.DEV) {
        initToolbar(stagewiseConfig);
    }
}

const app = createApp(App)
app.use(router)
app.mount('#app')

// Call the setup function when appropriate for your framework
setupStagewise();
