import { createRouter, createWebHistory } from 'vue-router'
import UserLogin from '../views/UserLogin.vue'
import Welcome from '../views/Welcome.vue'
import MedicineQuery from '../views/MedicineQuery.vue'
import AdminLogin from '../views/AdminLogin.vue'
import AdminLayout from '../components/AdminLayout.vue'
import AdminDashboard from '../views/AdminDashboard.vue'
import AdminUsers from '../views/AdminUsers.vue'
import AdminKeys from '../views/AdminKeys.vue'
import AdminMedicine from '../views/AdminMedicine.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: UserLogin
  },
  {
    path: '/welcome',
    name: 'Welcome',
    component: Welcome
  },
  {
    path: '/medicine-query',
    name: '<PERSON><PERSON>uery',
    component: MedicineQuery,
    meta: { requiresUserAuth: true }
  },
  {
    path: '/admin',
    name: 'AdminLogin',
    component: AdminLogin
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: AdminDashboard
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers
      },
      {
        path: 'keys',
        name: 'AdminKeys',
        component: AdminKeys
      },
      {
        path: 'medicine',
        name: 'AdminMedicine',
        component: AdminMedicine
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 管理员认证检查
  if (to.meta.requiresAuth) {
    const isAdminLoggedIn = localStorage.getItem('adminLoggedIn')
    if (!isAdminLoggedIn) {
      next('/admin')
      return
    }
  }

  // 用户认证检查
  if (to.meta.requiresUserAuth) {
    const isUserLoggedIn = localStorage.getItem('userLoggedIn')
    if (!isUserLoggedIn) {
      next('/login')
      return
    }
  }

  next()
})

export default router
