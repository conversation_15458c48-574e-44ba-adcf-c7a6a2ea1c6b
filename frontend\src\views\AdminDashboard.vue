<template>
  <div class="admin-dashboard">
    <div class="container">
      <div class="stats">
        <div class="stat-card">
          <div class="stat-number">{{ userLogins.length }}</div>
          <div class="stat-label">总登录次数</div>
        </div>
      </div>

      <div class="accounts-section">
        <div class="section-header">
          <h2>用户登录记录</h2>
          <div class="header-actions">
            <button
              @click="exportToExcel"
              class="export-btn"
              :disabled="isLoading || userLogins.length === 0"
            >
              导出Excel
            </button>
            <button
              @click="refreshData"
              class="refresh-btn"
              :disabled="isLoading"
            >
              {{ isLoading ? "刷新中..." : "刷新" }}
            </button>
          </div>
        </div>

        <div v-if="isLoading" class="loading">加载中...</div>

        <div v-else-if="loadError" class="error-state">
          <p class="error-message">{{ errorMessage }}</p>
          <button @click="refreshData" class="retry-btn">重试</button>
        </div>

        <div v-else-if="userLogins.length === 0" class="empty-state">
          <p>暂无用户登录记录</p>
        </div>

        <div v-else class="accounts-list">
          <div
            v-for="(login, index) in paginatedUserLogins"
            :key="login.id"
            class="account-card"
          >
            <div class="account-number">{{ index + 1 }}</div>
            <div class="account-info">
              <div class="phone-number">{{ login.phoneNumber }}</div>
              <div class="login-type-badge" :class="login.loginType">
                {{ login.loginType === "register" ? "注册并登录" : "登录" }}
                <span class="login-count">(第{{ login.loginCount }}次)</span>
              </div>
              <div class="login-time">
                {{ formatTime(login.loginTime) }}
              </div>
              <div class="key-info">
                密钥: {{ login.keyValue }}
                {{ login.keyDescription ? `(${login.keyDescription})` : "" }}
              </div>
              <div class="ip-address">IP: {{ login.ipAddress || "未知" }}</div>
            </div>
            <div class="action-buttons">
              <button
                @click="copyPhoneNumber(login.phoneNumber, $event)"
                class="copy-btn"
              >
                复制手机号
              </button>
              <button
                @click="deleteLoginRecord(login)"
                class="delete-btn"
                :disabled="deletingIds.has(login.id)"
              >
                {{ deletingIds.has(login.id) ? "删除中..." : "删除" }}
              </button>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <Pagination
          v-if="userLogins.length > 0"
          :current-page="currentPage"
          :total-items="userLogins.length"
          :page-size="pageSize"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import Pagination from "../components/Pagination.vue";

export default {
  name: "AdminDashboard",
  components: {
    Pagination,
  },
  data() {
    return {
      userLogins: [],
      isLoading: false,
      loadError: false,
      errorMessage: "",
      deletingIds: new Set(), // 正在删除的ID集合
      currentPage: 1,
      pageSize: 20,
    };
  },
  computed: {
    paginatedUserLogins() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.userLogins.slice(start, end);
    },
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.isLoading = true;
      this.loadError = false;
      this.errorMessage = "";
      try {
        const response = await axios.get("/api/user-logins");
        this.userLogins = response.data.userLogins || [];
      } catch (error) {
        console.error("加载数据失败:", error);
        this.loadError = true;
        this.errorMessage = "加载数据失败，请重试";
      } finally {
        this.isLoading = false;
      }
    },

    async refreshData() {
      await this.loadData();
    },

    formatTime(timeString) {
      const date = new Date(timeString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });
    },

    async copyPhoneNumber(phoneNumber, event) {
      // 优化事件对象处理，使用 currentTarget 更可靠
      let button = null;

      if (event && event.currentTarget) {
        button = event.currentTarget;
      } else if (event && event.target) {
        button = event.target;
      } else {
        console.warn("复制操作缺少事件对象，尝试查找按钮");
        // 降级方案：通过DOM查找
        const buttons = document.querySelectorAll(".copy-btn");
        button = Array.from(buttons).find(
          (btn) =>
            btn.closest(".account-card")?.querySelector(".phone-number")
              ?.textContent === phoneNumber
        );
      }

      if (!button) {
        console.error("无法找到复制按钮");
        return;
      }

      const originalText = button.textContent;

      try {
        await navigator.clipboard.writeText(phoneNumber);
        this.showCopySuccess(button, originalText);
      } catch (error) {
        console.error("复制失败，使用降级方案:", error);
        // 降级方案：使用传统方法复制
        try {
          const textArea = document.createElement("textarea");
          textArea.value = phoneNumber;
          textArea.style.position = "fixed";
          textArea.style.opacity = "0";
          textArea.style.left = "-9999px";
          document.body.appendChild(textArea);
          textArea.select();
          textArea.setSelectionRange(0, 99999); // 移动端兼容
          document.execCommand("copy");
          document.body.removeChild(textArea);
          this.showCopySuccess(button, originalText);
        } catch (fallbackError) {
          console.error("降级复制方案也失败:", fallbackError);
          this.showCopyError(button, originalText);
        }
      }
    },

    showCopySuccess(button, originalText) {
      button.textContent = "已复制";
      button.style.background = "#4CAF50";
      button.style.transform = "scale(0.95)";

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = "";
        button.style.transform = "";
      }, 1500);
    },

    showCopyError(button, originalText) {
      button.textContent = "复制失败";
      button.style.background = "#ff4757";

      setTimeout(() => {
        button.textContent = originalText;
        button.style.background = "";
      }, 2000);
    },

    async deleteLoginRecord(loginRecord) {
      // 确认删除
      if (
        !confirm(`确定要删除用户 "${loginRecord.phoneNumber}" 的登录记录吗？`)
      ) {
        return;
      }

      this.deletingIds.add(loginRecord.id);

      try {
        const response = await axios.delete(
          `/api/user-logins/${loginRecord.id}`
        );

        if (response.data.success) {
          // 从本地数据中移除
          this.userLogins = this.userLogins.filter(
            (item) => item.id !== loginRecord.id
          );
          console.log(`删除成功: ${loginRecord.phoneNumber}`);
        } else {
          console.error("删除失败:", response.data.message);
          alert(response.data.message || "删除失败，请重试");
        }
      } catch (error) {
        console.error("删除登录记录失败:", error);
        alert("删除失败，请重试");
      } finally {
        this.deletingIds.delete(loginRecord.id);
      }
    },

    exportToExcel() {
      if (this.userLogins.length === 0) {
        alert("暂无数据可导出");
        return;
      }

      try {
        // 准备导出数据
        const exportData = this.userLogins.map((login, index) => ({
          序号: index + 1,
          手机号: login.phoneNumber,
          登录类型: login.loginType === "register" ? "注册并登录" : "登录",
          登录次数: `第${login.loginCount}次`,
          登录时间: this.formatTime(login.loginTime),
          使用密钥: login.keyValue,
          密钥描述: login.keyDescription || "无",
          IP地址: login.ipAddress || "未知",
          记录ID: login.id,
        }));

        // 创建工作簿
        const wb = XLSX.utils.book_new();

        // 创建工作表
        const ws = XLSX.utils.json_to_sheet(exportData);

        // 设置列宽
        const colWidths = [
          { wch: 8 }, // 序号
          { wch: 15 }, // 手机号
          { wch: 12 }, // 登录类型
          { wch: 10 }, // 登录次数
          { wch: 20 }, // 登录时间
          { wch: 20 }, // 使用密钥
          { wch: 15 }, // 密钥描述
          { wch: 18 }, // IP地址
          { wch: 40 }, // 记录ID
        ];
        ws["!cols"] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "用户登录记录");

        // 生成文件名（包含当前时间）
        const now = new Date();
        const timestamp = now
          .toISOString()
          .slice(0, 19)
          .replace(/[:-]/g, "")
          .replace("T", "_");
        const filename = `用户登录记录_${timestamp}.xlsx`;

        // 导出文件
        const wbout = XLSX.write(wb, { bookType: "xlsx", type: "array" });
        const blob = new Blob([wbout], { type: "application/octet-stream" });
        saveAs(blob, filename);

        console.log(`导出成功: ${filename}`);
      } catch (error) {
        console.error("导出Excel失败:", error);
        alert("导出失败，请重试");
      }
    },

    handlePageChange(page) {
      this.currentPage = page;
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
    },
  },
};
</script>

<style scoped>
.admin-dashboard {
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.stats {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  display: inline-block;
  min-width: 200px;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 16px;
  color: #666;
}

.accounts-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.export-btn {
  padding: 14px 28px;
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  color: white;
  border: none;
  border-radius: 14px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4),
    0 4px 16px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.export-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.export-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  border-radius: 14px;
  pointer-events: none;
}

.export-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(16, 185, 129, 0.5),
    0 8px 24px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(16, 185, 129, 0.2);
}

.export-btn:hover:not(:disabled)::before {
  left: 100%;
}

.export-btn:active:not(:disabled) {
  transform: translateY(-2px) scale(1.01);
  transition: all 0.1s ease;
}

.export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.2);
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

.refresh-btn {
  padding: 14px 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #5b21b6 100%);
  color: white;
  border: none;
  border-radius: 14px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4),
    0 4px 16px rgba(102, 126, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.refresh-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.refresh-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  border-radius: 14px;
  pointer-events: none;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 50%, #4c1d95 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.5),
    0 8px 24px rgba(102, 126, 234, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(102, 126, 234, 0.2);
}

.refresh-btn:hover:not(:disabled)::before {
  left: 100%;
}

.refresh-btn:active:not(:disabled) {
  transform: translateY(-2px) scale(1.01);
  transition: all 0.1s ease;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

.loading,
.empty-state,
.error-state {
  padding: 40px;
  text-align: center;
  color: #666;
}

.error-state .error-message {
  color: #ff4757;
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 700;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
}

.retry-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.retry-btn:hover::before {
  left: 100%;
}

.accounts-list {
  max-height: 500px;
  overflow-y: auto;
}

/* 桌面端保持列表样式 */
.account-card {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s;
  gap: 20px;
}

.account-card:hover {
  background: #f8f9fa;
}

.account-card:last-child {
  border-bottom: none;
}

.account-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
}

.account-info {
  flex: 1;
}

.phone-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-family: "Courier New", monospace;
}

.login-type-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.login-type-badge.register {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.login-type-badge.login {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.login-count {
  font-size: 11px;
  opacity: 0.9;
  margin-left: 4px;
}

.login-time {
  font-size: 14px;
  color: #666;
}

.key-info {
  font-size: 13px;
  color: #888;
  margin-top: 2px;
  font-family: "Courier New", monospace;
}

.ip-address {
  font-size: 13px;
  color: #888;
  font-family: "Courier New", monospace;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.copy-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #5b21b6 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4),
    0 3px 12px rgba(102, 126, 234, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.copy-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.copy-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  border-radius: 12px;
  pointer-events: none;
}

.copy-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 50%, #4c1d95 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.5),
    0 6px 20px rgba(102, 126, 234, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(102, 126, 234, 0.2);
}

.copy-btn:hover::before {
  left: 100%;
}

.copy-btn:active {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.delete-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 24px rgba(239, 68, 68, 0.4),
    0 3px 12px rgba(239, 68, 68, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.delete-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.delete-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%
  );
  border-radius: 12px;
  pointer-events: none;
}

.delete-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.5),
    0 6px 20px rgba(239, 68, 68, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 1px rgba(239, 68, 68, 0.2);
}

.delete-btn:hover:not(:disabled)::before {
  left: 100%;
}

.delete-btn:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  transition: all 0.1s ease;
}

.delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }

  .container {
    max-width: 100%;
  }

  .stats {
    margin-bottom: 24px;
  }

  .stat-card {
    width: 100%;
    box-sizing: border-box;
    padding: 28px 24px;
    border-radius: 16px;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .stat-number {
    font-size: 36px;
    margin-bottom: 10px;
    font-weight: 700;
  }

  .stat-label {
    font-size: 16px;
    font-weight: 500;
  }

  .accounts-section {
    border-radius: 16px;
    overflow: visible;
    box-shadow: none;
    background: transparent;
  }

  .section-header {
    flex-direction: column;
    gap: 18px;
    padding: 22px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 8px;
  }

  .section-header h2 {
    font-size: 18px;
    text-align: center;
    margin: 0;
    font-weight: 600;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
    justify-content: center;
    width: 100%;
  }

  .export-btn {
    flex: 1;
    padding: 12px 20px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    min-height: 44px;
  }

  .export-btn:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
  }

  .refresh-btn {
    flex: 1;
    padding: 12px 20px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    min-height: 44px;
  }

  .refresh-btn:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  .accounts-list {
    max-height: 65vh;
    padding: 16px;
    background: transparent;
    overflow-y: auto;
  }

  /* 移动端改为卡片式布局 */
  .account-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 16px;
    padding: 20px;
    border: none;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    transition: all 0.3s ease;
    position: relative;
  }

  .account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    background: white;
  }

  .account-card:last-child {
    margin-bottom: 0;
  }

  .account-number {
    position: absolute;
    top: -8px;
    left: -8px;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    z-index: 1;
  }

  .account-info {
    text-align: center;
    padding: 8px 0;
  }

  .wechat-id {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    color: #1a202c;
    word-break: break-all;
    line-height: 1.4;
  }

  .submit-time {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    align-items: stretch;
  }

  .copy-btn {
    flex: 1;
    text-align: center;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.5px;
  }

  .copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .copy-btn:active {
    transform: translateY(0);
  }

  .delete-btn {
    flex: 1;
    text-align: center;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 700;
    border-radius: 12px;
    background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.3);
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.5px;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .delete-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
  }

  .delete-btn:active:not(:disabled) {
    transform: translateY(0);
  }

  .delete-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 16px rgba(255, 71, 87, 0.2);
  }

  .loading,
  .empty-state,
  .error-state {
    padding: 36px 24px;
    font-size: 16px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin: 16px;
  }

  .error-state .error-message {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .retry-btn {
    padding: 12px 24px;
    font-size: 15px;
    border-radius: 10px;
    font-weight: 600;
  }
}
</style>
