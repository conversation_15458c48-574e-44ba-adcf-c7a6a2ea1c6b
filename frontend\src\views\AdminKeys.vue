<template>
  <div class="admin-keys">
    <div class="keys-header">
      <div class="header-actions">
        <button @click="showAddModal = true" class="add-btn">
          <span class="btn-icon">🔑</span>
          <span class="btn-text">生成密钥</span>
        </button>
        <button @click="refreshData" class="refresh-btn" :disabled="isLoading">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">{{ isLoading ? "刷新中..." : "刷新" }}</span>
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="loading">加载中...</div>

    <div v-else-if="loadError" class="error-state">
      <p class="error-message">{{ errorMessage }}</p>
      <button @click="refreshData" class="retry-btn">重试</button>
    </div>

    <div v-else-if="accessKeys.length === 0" class="empty-state">
      <div class="empty-icon">🔑</div>
      <p>暂无密钥</p>
      <p class="empty-tip">点击"生成密钥"按钮创建第一个密钥</p>
    </div>

    <div v-else class="keys-list">
      <div class="keys-table">
        <div class="table-header">
          <div class="header-cell">密钥</div>
          <div class="header-cell">类型</div>
          <div class="header-cell">状态</div>
          <div class="header-cell">使用次数</div>
          <div class="header-cell">创建时间</div>
          <div class="header-cell">过期时间</div>
          <div class="header-cell">操作</div>
        </div>

        <div v-for="key in paginatedKeys" :key="key.id" class="table-row">
          <div class="table-cell">
            <div class="key-display">
              <span class="key-value">{{ key.keyValue }}</span>
              <button @click="copyKey(key.keyValue)" class="copy-key-btn">
                📋
              </button>
            </div>
          </div>
          <div class="table-cell">
            <span class="type-badge" :class="key.type">
              {{ getTypeText(key.type) }}
            </span>
          </div>
          <div class="table-cell">
            <span class="status-badge" :class="key.status">
              {{ getStatusText(key.status) }}
            </span>
          </div>
          <div class="table-cell">{{ key.usageCount || 0 }}</div>
          <div class="table-cell">{{ formatTime(key.createTime) }}</div>
          <div class="table-cell">
            {{ key.expireTime ? formatTime(key.expireTime) : "永不过期" }}
          </div>
          <div class="table-cell">
            <div class="action-buttons">
              <button
                v-if="key.status === 'active'"
                @click="toggleKeyStatus(key)"
                class="disable-btn"
                :disabled="updatingIds.has(key.id)"
              >
                {{ updatingIds.has(key.id) ? "禁用中..." : "🚫 禁用" }}
              </button>
              <button
                v-else
                @click="toggleKeyStatus(key)"
                class="enable-btn"
                :disabled="updatingIds.has(key.id)"
              >
                {{ updatingIds.has(key.id) ? "启用中..." : "✅ 启用" }}
              </button>
              <button
                @click="deleteKey(key)"
                class="delete-btn"
                :disabled="deletingIds.has(key.id)"
              >
                {{ deletingIds.has(key.id) ? "删除中..." : "🗑️ 删除" }}
              </button>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <Pagination
          v-if="accessKeys.length > 0"
          :current-page="currentPage"
          :total-items="accessKeys.length"
          :page-size="pageSize"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 生成密钥模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeAddModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>生成新密钥</h3>
          <button @click="closeAddModal" class="close-btn">✕</button>
        </div>

        <form @submit.prevent="generateKey" class="modal-form">
          <div class="form-group">
            <label>密钥类型</label>
            <select v-model="newKey.type" class="form-select" required>
              <option value="">请选择密钥类型</option>
              <option value="temporary">临时密钥（7天）</option>
              <option value="monthly">月度密钥（30天）</option>
              <option value="permanent">永久密钥</option>
            </select>
          </div>

          <div class="form-group">
            <label>备注说明</label>
            <input
              v-model="newKey.description"
              type="text"
              placeholder="请输入备注说明（可选）"
              class="form-input"
            />
          </div>

          <div class="form-actions">
            <button type="button" @click="closeAddModal" class="cancel-btn">
              取消
            </button>
            <button type="submit" class="submit-btn" :disabled="isGenerating">
              {{ isGenerating ? "生成中..." : "生成密钥" }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import Pagination from "../components/Pagination.vue";

export default {
  name: "AdminKeys",
  components: {
    Pagination,
  },
  data() {
    return {
      accessKeys: [],
      isLoading: false,
      loadError: false,
      errorMessage: "",
      deletingIds: new Set(),
      updatingIds: new Set(),

      // 生成密钥相关
      showAddModal: false,
      isGenerating: false,
      newKey: {
        type: "",
        description: "",
      },

      // 分页相关
      currentPage: 1,
      pageSize: 20,
    };
  },
  computed: {
    paginatedKeys() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.accessKeys.slice(start, end);
    },
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.isLoading = true;
      this.loadError = false;
      this.errorMessage = "";

      try {
        const response = await axios.get("/api/admin/keys");
        this.accessKeys = response.data.keys || [];
      } catch (error) {
        console.error("加载密钥列表失败:", error);
        this.loadError = true;
        this.errorMessage = "加载密钥列表失败，请重试";
      } finally {
        this.isLoading = false;
      }
    },

    async refreshData() {
      await this.loadData();
    },

    getTypeText(type) {
      const typeMap = {
        temporary: "临时",
        monthly: "月度",
        permanent: "永久",
      };
      return typeMap[type] || "未知";
    },

    getStatusText(status) {
      const statusMap = {
        active: "有效",
        disabled: "已禁用",
        expired: "已过期",
      };
      return statusMap[status] || "未知";
    },

    formatTime(timeString) {
      const date = new Date(timeString);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    async copyKey(keyValue) {
      try {
        await navigator.clipboard.writeText(keyValue);
        alert("密钥已复制到剪贴板");
      } catch (error) {
        console.error("复制失败:", error);
        alert("复制失败，请手动复制");
      }
    },

    closeAddModal() {
      this.showAddModal = false;
      this.newKey = {
        type: "",
        description: "",
      };
    },

    async generateKey() {
      this.isGenerating = true;

      try {
        const response = await axios.post("/api/admin/keys", this.newKey);

        if (response.data.success) {
          alert("密钥生成成功");
          this.closeAddModal();
          await this.loadData();
        } else {
          alert(response.data.message || "生成失败");
        }
      } catch (error) {
        console.error("生成密钥失败:", error);
        alert(error.response?.data?.message || "生成密钥失败，请重试");
      } finally {
        this.isGenerating = false;
      }
    },

    async toggleKeyStatus(key) {
      this.updatingIds.add(key.id);

      try {
        const newStatus = key.status === "active" ? "disabled" : "active";
        const response = await axios.put(`/api/admin/keys/${key.id}/status`, {
          status: newStatus,
        });

        if (response.data.success) {
          key.status = newStatus;
          alert(`密钥已${newStatus === "active" ? "启用" : "禁用"}`);
        } else {
          alert(response.data.message || "操作失败");
        }
      } catch (error) {
        console.error("更新密钥状态失败:", error);
        alert(error.response?.data?.message || "操作失败，请重试");
      } finally {
        this.updatingIds.delete(key.id);
      }
    },

    async deleteKey(key) {
      if (!confirm(`确定要删除密钥 "${key.keyValue}" 吗？`)) {
        return;
      }

      this.deletingIds.add(key.id);

      try {
        const response = await axios.delete(`/api/admin/keys/${key.id}`);

        if (response.data.success) {
          alert("密钥删除成功");
          await this.loadData();
        } else {
          alert(response.data.message || "删除失败");
        }
      } catch (error) {
        console.error("删除密钥失败:", error);
        alert(error.response?.data?.message || "删除密钥失败，请重试");
      } finally {
        this.deletingIds.delete(key.id);
      }
    },

    handlePageChange(page) {
      this.currentPage = page;
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
    },
  },
};
</script>

<style scoped>
.admin-keys {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 80vh;
  height: auto;
}

.keys-header {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.add-btn,
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 700;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
}

.add-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.add-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.add-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.add-btn:hover::before {
  left: 100%;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.refresh-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6b46c1 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.refresh-btn:hover:not(:disabled)::before {
  left: 100%;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.loading,
.error-state,
.empty-state {
  padding: 40px;
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.empty-tip {
  color: #999;
  font-size: 14px;
}

.error-message {
  color: #ff4757;
  margin-bottom: 20px;
  font-size: 16px;
}

.retry-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-btn:hover {
  background: #5a6fd8;
}

.keys-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 0.8fr 0.8fr 0.8fr 1.2fr 1.2fr 1.5fr;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
}

.header-cell {
  padding: 15px 20px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 0.8fr 0.8fr 0.8fr 1.2fr 1.2fr 1.5fr;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.3s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 20px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.key-display {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.key-value {
  font-family: "Courier New", monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copy-key-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.3s;
}

.copy-key-btn:hover {
  background: #e9ecef;
}

.type-badge,
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.type-badge.temporary {
  background: #ffc107;
  color: #333;
}

.type-badge.monthly {
  background: #17a2b8;
  color: white;
}

.type-badge.permanent {
  background: #6f42c1;
  color: white;
}

.status-badge.active {
  background: #28a745;
  color: white;
}

.status-badge.disabled {
  background: #6c757d;
  color: white;
}

.status-badge.expired {
  background: #dc3545;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.enable-btn,
.disable-btn,
.delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.enable-btn {
  background: #28a745;
  color: white;
}

.enable-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.disable-btn {
  background: #ffc107;
  color: #333;
}

.disable-btn:hover:not(:disabled) {
  background: #e0a800;
  transform: translateY(-1px);
}

.delete-btn {
  background: #ff4757;
  color: white;
}

.delete-btn:hover:not(:disabled) {
  background: #ff3838;
  transform: translateY(-1px);
}

.enable-btn:disabled,
.disable-btn:disabled,
.delete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #333;
}

.modal-form {
  padding: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #667eea;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .admin-keys {
    min-height: 70vh;
  }

  .keys-header {
    padding: 15px 20px;
  }

  .header-actions {
    flex-direction: column;
  }

  .add-btn,
  .refresh-btn {
    justify-content: center;
  }

  .keys-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    grid-template-columns: 180px 60px 60px 60px 100px 100px 120px;
    min-width: 680px;
  }

  .header-cell,
  .table-cell {
    padding: 12px 10px;
    font-size: 12px;
  }

  .key-value {
    font-size: 10px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .enable-btn,
  .disable-btn,
  .delete-btn {
    font-size: 10px;
    padding: 4px 8px;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-form {
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
