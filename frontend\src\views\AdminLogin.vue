<template>
  <div class="admin-login">
    <div class="container">
      <div class="login-card">
        <h1 class="title">管理员登录</h1>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="input-group">
            <input
              v-model="username"
              type="text"
              placeholder="用户名"
              class="input-field"
              :class="{ error: hasError }"
            />
          </div>

          <div class="input-group">
            <input
              v-model="password"
              type="password"
              placeholder="密码"
              class="input-field"
              :class="{ error: hasError }"
              @input="clearError"
            />
            <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
          </div>

          <button type="submit" class="login-btn" :disabled="isLogging">
            {{ isLogging ? "登录中..." : "登录" }}
          </button>
        </form>

        <div class="back-link">
          <router-link to="/" class="back-text">返回用户页面</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "AdminLogin",
  data() {
    return {
      username: "",
      password: "",
      hasError: false,
      errorMessage: "",
      isLogging: false,
    };
  },
  methods: {
    clearError() {
      this.hasError = false;
      this.errorMessage = "";
    },

    async handleLogin() {
      if (!this.username.trim() || !this.password.trim()) {
        this.hasError = true;
        this.errorMessage = "请输入用户名和密码";
        return;
      }

      this.isLogging = true;

      try {
        console.log("发送登录请求...");
        const response = await axios.post(
          "/api/admin/login",
          {
            username: this.username.trim(),
            password: this.password.trim(),
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        console.log("登录响应:", response.data);

        if (response.data.success) {
          localStorage.setItem("adminLoggedIn", "true");
          // 保存当前管理员信息
          localStorage.setItem(
            "currentAdmin",
            JSON.stringify(response.data.admin)
          );
          this.$router.push("/admin/dashboard").catch((err) => {
            console.error("路由跳转失败:", err);
          });
        } else {
          this.hasError = true;
          this.errorMessage = response.data.message || "用户名或密码错误";
        }
      } catch (error) {
        console.error("管理员登录失败:", error);
        this.hasError = true;

        if (error.code === "ECONNABORTED") {
          this.errorMessage = "请求超时，请检查网络连接";
        } else if (error.response) {
          // 服务器响应了错误状态码
          const status = error.response.status;
          const message = error.response.data?.message;

          if (status === 401) {
            this.errorMessage = message || "用户名或密码错误";
          } else if (status === 500) {
            this.errorMessage = "服务器内部错误，请稍后重试";
          } else {
            this.errorMessage = message || `请求失败 (${status})`;
          }
        } else if (error.request) {
          // 请求发出但没有收到响应
          this.errorMessage = "无法连接到服务器，请检查后端服务是否启动";
        } else {
          this.errorMessage = "登录失败，请重试";
        }
      } finally {
        this.isLogging = false;
      }
    },
  },
};
</script>

<style scoped>
.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
}

.login-form {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-field {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
}

.input-field.error {
  border-color: #ff4757;
}

.error-message {
  color: #ff4757;
  font-size: 14px;
  margin-top: 5px;
}

.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.back-link {
  margin-top: 20px;
}

.back-text {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
}

.back-text:hover {
  text-decoration: underline;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-card {
    padding: 30px 20px;
  }

  .title {
    font-size: 20px;
  }

  .input-field,
  .login-btn {
    padding: 12px;
    font-size: 16px;
  }
}
</style>
