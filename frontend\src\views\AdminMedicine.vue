<template>
  <div class="admin-medicine">
    <div class="placeholder-content">
      <div class="placeholder-icon">🌿</div>
      <h2 class="placeholder-title">中药药方管理</h2>
      <p class="placeholder-description">
        此功能正在开发中，敬请期待...
      </p>
      <div class="placeholder-features">
        <div class="feature-item">
          <span class="feature-icon">📋</span>
          <span class="feature-text">药方录入管理</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">🔍</span>
          <span class="feature-text">药方查询检索</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">📊</span>
          <span class="feature-text">用药统计分析</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">💾</span>
          <span class="feature-text">数据导入导出</span>
        </div>
      </div>
      <div class="placeholder-actions">
        <button class="coming-soon-btn" disabled>
          <span class="btn-icon">⏳</span>
          <span class="btn-text">敬请期待</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AdminMedicine',
  data() {
    return {
      // 占位数据
    }
  },
  mounted() {
    console.log('中药药方管理页面已加载')
  }
}
</script>

<style scoped>
.admin-medicine {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  max-width: 600px;
  padding: 60px 40px;
}

.placeholder-icon {
  font-size: 120px;
  margin-bottom: 30px;
  opacity: 0.8;
}

.placeholder-title {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.placeholder-description {
  font-size: 18px;
  color: #666;
  margin-bottom: 50px;
  line-height: 1.6;
}

.placeholder-features {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 50px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  transition: all 0.3s;
}

.feature-item:hover {
  background: rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 24px;
}

.feature-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.placeholder-actions {
  margin-top: 40px;
}

.coming-soon-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  background: #95a5a6;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-icon {
  font-size: 18px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .placeholder-content {
    padding: 40px 20px;
  }
  
  .placeholder-icon {
    font-size: 80px;
    margin-bottom: 20px;
  }
  
  .placeholder-title {
    font-size: 28px;
    margin-bottom: 15px;
  }
  
  .placeholder-description {
    font-size: 16px;
    margin-bottom: 40px;
  }
  
  .placeholder-features {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
  }
  
  .feature-item {
    padding: 15px;
  }
  
  .feature-icon {
    font-size: 20px;
  }
  
  .feature-text {
    font-size: 14px;
  }
  
  .coming-soon-btn {
    padding: 12px 24px;
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.placeholder-content {
  animation: fadeInUp 0.6s ease-out;
}

.feature-item {
  animation: fadeInUp 0.6s ease-out;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }
</style>
