<template>
  <div class="medicine-query">
    <div class="container">
      <!-- 用户信息栏 -->
      <div class="user-info-bar">
        <div class="user-welcome">
          <span class="welcome-text"
            >欢迎，{{ currentUser?.phoneNumber || "用户" }}</span
          >
        </div>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>

      <!-- 查询区域 -->
      <div class="query-section">
        <h1 class="page-title">中药方剂查询系统</h1>

        <div class="search-form">
          <div class="search-input-group">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="请输入中药名称、功效或症状关键词"
              class="search-input"
              @keyup.enter="searchMedicine"
            />
            <button
              @click="searchMedicine"
              class="search-btn"
              :disabled="isSearching"
            >
              <span class="search-icon">🔍</span>
              <span class="search-text">{{
                isSearching ? "查询中..." : "查询"
              }}</span>
            </button>
          </div>

          <div class="search-filters">
            <select v-model="searchType" class="filter-select">
              <option value="all">全部</option>
              <option value="name">药材名称</option>
              <option value="effect">功效</option>
              <option value="symptom">症状</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 查询结果区域 -->
      <div class="results-section">
        <div v-if="isSearching" class="loading">
          <div class="loading-spinner"></div>
          <p>正在查询中药方剂...</p>
        </div>

        <div v-else-if="searchError" class="error-state">
          <div class="error-icon">❌</div>
          <p class="error-message">{{ errorMessage }}</p>
          <button @click="searchMedicine" class="retry-btn">重新查询</button>
        </div>

        <div v-else-if="searchResults.length > 0" class="results-list">
          <div class="results-header">
            <h3>查询结果 ({{ searchResults.length }} 条)</h3>
          </div>

          <div class="medicine-cards">
            <div
              v-for="medicine in searchResults"
              :key="medicine.id"
              class="medicine-card"
            >
              <div class="medicine-header">
                <h4 class="medicine-name">{{ medicine.name }}</h4>
                <span class="medicine-type">{{ medicine.type }}</span>
              </div>

              <div class="medicine-content">
                <div class="medicine-info">
                  <div class="info-item">
                    <span class="info-label">功效：</span>
                    <span class="info-value">{{ medicine.effects }}</span>
                  </div>

                  <div class="info-item">
                    <span class="info-label">主治：</span>
                    <span class="info-value">{{ medicine.indications }}</span>
                  </div>

                  <div class="info-item">
                    <span class="info-label">用法用量：</span>
                    <span class="info-value">{{ medicine.dosage }}</span>
                  </div>

                  <div v-if="medicine.contraindications" class="info-item">
                    <span class="info-label">禁忌：</span>
                    <span class="info-value contraindication">{{
                      medicine.contraindications
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="hasSearched" class="empty-state">
          <div class="empty-icon">🔍</div>
          <p>未找到相关的中药方剂信息</p>
          <p class="empty-tip">请尝试使用其他关键词查询</p>
        </div>

        <div v-else class="welcome-state">
          <div class="welcome-icon">🌿</div>
          <h3>欢迎使用中药方剂查询系统</h3>
          <p>请在上方输入框中输入中药名称、功效或症状关键词进行查询</p>

          <div class="quick-search">
            <h4>热门查询：</h4>
            <div class="quick-tags">
              <button
                v-for="tag in quickSearchTags"
                :key="tag"
                @click="quickSearch(tag)"
                class="quick-tag"
              >
                {{ tag }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "MedicineQuery",
  data() {
    return {
      currentUser: null,
      searchKeyword: "",
      searchType: "all",
      isSearching: false,
      searchError: false,
      errorMessage: "",
      searchResults: [],
      hasSearched: false,
      quickSearchTags: [
        "人参",
        "当归",
        "黄芪",
        "枸杞",
        "甘草",
        "川芎",
        "白术",
        "茯苓",
      ],
    };
  },
  mounted() {
    // 获取当前用户信息
    const userInfo = localStorage.getItem("currentUser");
    if (userInfo) {
      this.currentUser = JSON.parse(userInfo);
    }

    // 检查用户是否已登录
    const isLoggedIn = localStorage.getItem("userLoggedIn");
    if (!isLoggedIn) {
      this.$router.push("/login");
    }
  },
  methods: {
    async searchMedicine() {
      if (!this.searchKeyword.trim()) {
        alert("请输入查询关键词");
        return;
      }

      this.isSearching = true;
      this.searchError = false;
      this.hasSearched = true;

      try {
        const response = await axios.post("/api/medicine/search", {
          keyword: this.searchKeyword.trim(),
          type: this.searchType,
        });

        if (response.data.success) {
          this.searchResults = response.data.medicines || [];
        } else {
          this.searchError = true;
          this.errorMessage = response.data.message || "查询失败";
        }
      } catch (error) {
        console.error("查询中药方剂失败:", error);
        this.searchError = true;
        this.errorMessage = "查询服务暂时不可用，请稍后重试";
      } finally {
        this.isSearching = false;
      }
    },

    quickSearch(tag) {
      this.searchKeyword = tag;
      this.searchMedicine();
    },

    logout() {
      localStorage.removeItem("userLoggedIn");
      localStorage.removeItem("currentUser");
      this.$router.push("/login");
    },
  },
};
</script>

<style scoped>
.medicine-query {
  min-height: 100vh;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.user-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 25px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.logout-btn {
  padding: 8px 16px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.logout-btn:hover {
  background: #ff3838;
}

.query-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 20px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  margin-bottom: 30px;
}

.search-form {
  max-width: 600px;
  margin: 0 auto;
}

.search-input-group {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 25px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
}

.search-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-filters {
  display: flex;
  justify-content: center;
}

.filter-select {
  padding: 10px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.results-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  min-height: 400px;
  padding: 30px;
}

.loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-state,
.empty-state,
.welcome-state {
  text-align: center;
  padding: 60px 20px;
}

.error-icon,
.empty-icon,
.welcome-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.error-message {
  color: #ff4757;
  font-size: 16px;
  margin-bottom: 20px;
}

.retry-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.retry-btn:hover {
  background: #5a6fd8;
}

.empty-tip {
  color: #999;
  font-size: 14px;
}

.welcome-state h3 {
  color: #333;
  margin-bottom: 15px;
}

.welcome-state p {
  color: #666;
  margin-bottom: 30px;
}

.quick-search h4 {
  color: #333;
  margin-bottom: 15px;
}

.quick-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.quick-tag {
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid #667eea;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.quick-tag:hover {
  background: #667eea;
  color: white;
}

.results-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.results-header h3 {
  color: #333;
  font-size: 20px;
  margin: 0;
}

.medicine-cards {
  display: grid;
  gap: 20px;
}

.medicine-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.medicine-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.medicine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.medicine-name {
  font-size: 22px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.medicine-type {
  padding: 4px 12px;
  background: #667eea;
  color: white;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

.medicine-content {
  line-height: 1.6;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.info-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
  margin-right: 10px;
}

.info-value {
  color: #666;
  flex: 1;
}

.contraindication {
  color: #ff4757;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .medicine-query {
    padding: 15px;
  }

  .user-info-bar {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }

  .query-section {
    padding: 30px 20px;
  }

  .page-title {
    font-size: 28px;
    margin-bottom: 25px;
  }

  .search-input-group {
    flex-direction: column;
  }

  .search-input,
  .search-btn {
    width: 100%;
  }

  .results-section {
    padding: 20px;
  }

  .medicine-card {
    padding: 20px;
  }

  .medicine-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .medicine-name {
    font-size: 18px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 5px;
    min-width: auto;
  }

  .quick-tags {
    justify-content: flex-start;
  }
}
</style>
