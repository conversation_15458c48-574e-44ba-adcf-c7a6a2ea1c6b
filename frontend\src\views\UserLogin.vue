<template>
  <div class="user-login">
    <div class="container">
      <div class="login-card">
        <h1 class="title">中药方剂查询</h1>

        <form @submit.prevent="handleSubmit" class="login-form">
          <div class="input-group">
            <input
              v-model="phoneNumber"
              type="tel"
              placeholder="请输入手机号"
              class="input-field"
              :class="{ error: hasError }"
              @input="clearError"
            />
          </div>

          <div class="input-group">
            <input
              v-model="accessKey"
              type="password"
              placeholder="请输入密钥"
              class="input-field"
              :class="{ error: hasError }"
              @input="clearError"
            />
            <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
          </div>

          <button type="submit" class="login-btn" :disabled="isSubmitting">
            {{ isSubmitting ? "登录查询" : "登录查询" }}
          </button>
        </form>

        <div class="admin-link">
          <router-link to="/admin" class="admin-text">管理员入口</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios";

export default {
  name: "UserLogin",
  data() {
    return {
      phoneNumber: "",
      accessKey: "",
      hasError: false,
      errorMessage: "",
      isSubmitting: false,
    };
  },
  methods: {
    validateInput() {
      if (!this.phoneNumber.trim()) {
        return "请输入手机号";
      }

      // 简单的手机号验证
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.phoneNumber.trim())) {
        return "请输入正确的手机号格式";
      }

      if (!this.accessKey.trim()) {
        return "请输入密钥";
      }

      return null;
    },

    clearError() {
      this.hasError = false;
      this.errorMessage = "";
    },

    async handleSubmit() {
      const error = this.validateInput();
      if (error) {
        this.hasError = true;
        this.errorMessage = error;
        return;
      }

      this.isSubmitting = true;

      try {
        console.log("用户登录:", this.phoneNumber.trim());
        const response = await axios.post(
          "/api/user/login",
          {
            phoneNumber: this.phoneNumber.trim(),
            accessKey: this.accessKey.trim(),
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        console.log("提交响应:", response.data);

        if (response.data.success) {
          // 保存用户信息到localStorage
          localStorage.setItem("userLoggedIn", "true");
          localStorage.setItem(
            "currentUser",
            JSON.stringify(response.data.user)
          );
          // 跳转到中药查询页面
          this.$router.push("/medicine-query").catch((err) => {
            console.error("路由跳转失败:", err);
          });
        } else {
          this.hasError = true;
          this.errorMessage =
            response.data.message || "登录失败，请检查手机号和密钥";
        }
      } catch (error) {
        console.error("提交微信号失败:", error);
        this.hasError = true;

        if (error.code === "ECONNABORTED") {
          this.errorMessage = "请求超时，请检查网络连接";
        } else if (error.response) {
          const status = error.response.status;
          const message = error.response.data?.message;

          if (status === 400) {
            this.errorMessage = message || "输入数据有误";
          } else if (status === 500) {
            this.errorMessage = "服务器内部错误，请稍后重试";
          } else {
            this.errorMessage = message || `提交失败 (${status})`;
          }
        } else if (error.request) {
          this.errorMessage = "无法连接到服务器，请检查后端服务是否启动";
        } else {
          this.errorMessage = "提交失败，请重试";
        }
      } finally {
        this.isSubmitting = false;
      }
    },
  },
};
</script>

<style scoped>
.user-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-image: url("../assets/bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

.container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  font-weight: 600;
}

.login-form {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

.input-field {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 16px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.input-field:focus {
  outline: none;
  border-color: #667eea;
}

.input-field.error {
  border-color: #ff4757;
}

.error-message {
  color: #ff4757;
  font-size: 14px;
  margin-top: 5px;
}

.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-link {
  margin-top: 20px;
}

.admin-text {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
}

.admin-text:hover {
  text-decoration: underline;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-login {
    padding: 15px;
    min-height: 100vh;
    display: flex;
    align-items: center;
  }

  .container {
    width: 100%;
    max-width: 100%;
  }

  .login-card {
    padding: 35px 25px;
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .title {
    font-size: 22px;
    margin-bottom: 35px;
    font-weight: 600;
    line-height: 1.3;
  }

  .input-field {
    padding: 16px 18px;
    font-size: 16px;
    border-radius: 12px;
    border: 2px solid #e8ecf0;
    margin-bottom: 8px;
  }

  .input-field:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .login-btn {
    padding: 16px;
    font-size: 17px;
    font-weight: 600;
    border-radius: 12px;
    margin-top: 10px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  .login-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  }

  .admin-link {
    margin-top: 25px;
  }

  .admin-text {
    font-size: 15px;
    font-weight: 500;
  }

  .error-message {
    font-size: 13px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 71, 87, 0.1);
    border-radius: 8px;
    border-left: 3px solid #ff4757;
  }
}
</style>
