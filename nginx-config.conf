# Nginx配置文件 - 微信号收集系统（已更新支持IP收集和404修复）
# 请将此配置添加到您的Nginx配置中或替换现有配置

server {
    listen 80;
    server_name ev.zhcv.cn;

    # 网站根目录 - 指向前端构建文件（请根据实际宝塔部署路径调整）
    # 常见宝塔路径格式：/www/wwwroot/域名/
    root /www/wwwroot/ev.zhcv.cn/frontend/dist;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 前端静态文件 - 修复Vue Router History模式404问题
    location / {
        try_files $uri $uri/ /index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理到后端 - 支持IP地址传递
    location /api {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 日志
    access_log /www/wwwlogs/wechat-login-access.log;
    error_log /www/wwwlogs/wechat-login-error.log;
}

# HTTPS配置（可选，建议启用）
# server {
#     listen 443 ssl http2;
#     server_name ev.zhcv.cn;
#     
#     # SSL证书配置
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置与HTTP相同...
# }

# HTTP重定向到HTTPS（启用HTTPS时取消注释）
# server {
#     listen 80;
#     server_name ev.zhcv.cn;
#     return 301 https://$server_name$request_uri;
# }
