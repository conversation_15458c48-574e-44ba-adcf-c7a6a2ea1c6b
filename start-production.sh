#!/bin/bash

# 微信号收集系统生产环境启动脚本

echo "=== 微信号收集系统部署脚本 ==="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装 PM2..."
    npm install -g pm2
fi

# 进入项目目录
cd "$(dirname "$0")"

echo "📁 当前目录: $(pwd)"

# 构建前端
echo "🔨 构建前端项目..."
cd frontend
npm install
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo "✅ 前端构建完成"

# 返回根目录
cd ..

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
npm install --production

if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi

echo "✅ 后端依赖安装完成"

# 返回根目录
cd ..

# 创建日志目录
mkdir -p logs

# 停止现有的PM2进程（如果存在）
echo "🛑 停止现有服务..."
pm2 stop wechat-login-api 2>/dev/null || true
pm2 delete wechat-login-api 2>/dev/null || true

# 启动后端服务
echo "🚀 启动后端服务..."
pm2 start ecosystem.config.js

if [ $? -ne 0 ]; then
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 保存PM2配置
pm2 save
pm2 startup

echo "✅ 后端服务启动成功"

# 显示服务状态
echo ""
echo "📊 服务状态:"
pm2 status

echo ""
echo "🎉 部署完成！"
echo "📡 后端API: http://localhost:3001"
echo "🏥 健康检查: http://localhost:3001/api/health"
echo "📁 前端文件: ./frontend/dist"
echo ""
echo "📝 下一步："
echo "1. 配置 Nginx 反向代理"
echo "2. 设置域名解析"
echo "3. 配置 SSL 证书（可选）"
echo ""
echo "🔧 管理命令："
echo "pm2 status          - 查看服务状态"
echo "pm2 logs            - 查看日志"
echo "pm2 restart all     - 重启服务"
echo "pm2 stop all        - 停止服务"
